import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:get/get.dart';

class ImageCropService {
  static final ImagePicker _picker = ImagePicker();

  /// 选择并裁剪图片，返回裁剪后的图片路径
  static Future<String?> pickAndCropImage({
    required double aspectRatioX,
    required double aspectRatioY,
    String title = '裁剪图片',
  }) async {
    try {
      // 选择图片
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 90,
      );

      if (pickedFile == null) return null;

      // 裁剪图片
      final CroppedFile? croppedFile = await ImageCropper().cropImage(
        sourcePath: pickedFile.path,
        aspectRatio: CropAspectRatio(
          ratioX: aspectRatioX,
          ratioY: aspectRatioY,
        ),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: title,
            toolbarColor: Get.theme.primaryColor,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: true,
            aspectRatioPresets: [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.square,
              CropAspectRatioPresetCustom(),
            ],
          ),
          IOSUiSettings(
            title: title,
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPresets: [
              CropAspectRatioPreset.original,
              CropAspectRatioPreset.square,
              CropAspectRatioPresetCustom(),
            ],
          ),
          WebUiSettings(
            context: Get.context!,
            presentStyle: CropperPresentStyle.dialog,
            boundary: const CroppieBoundary(
              width: 520,
              height: 520,
            ),
            viewPort: CropPieViewPort(
              width: 480,
              height: 480 * aspectRatioY / aspectRatioX,
              type: 'circle',
            ),
            enableExif: true,
            enableZoom: true,
            showZoomer: true,
          ),
        ],
      );

      if (croppedFile == null) return null;

      // 保存裁剪后的图片到应用目录
      final String savedPath = await _saveCroppedImage(croppedFile);
      return savedPath;
    } catch (e) {
      print('图片裁剪失败: $e');
      Get.snackbar('错误', '图片裁剪失败: $e');
      return null;
    }
  }

  /// 保存裁剪后的图片到应用目录
  static Future<String> _saveCroppedImage(CroppedFile croppedFile) async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String imagesDir = path.join(appDir.path, 'images');
    
    // 确保目录存在
    await Directory(imagesDir).create(recursive: true);
    
    // 生成唯一文件名
    final String fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
    final String savedPath = path.join(imagesDir, fileName);
    
    // 复制文件
    await File(croppedFile.path).copy(savedPath);
    
    return savedPath;
  }

  /// 删除图片文件
  static Future<void> deleteImage(String imagePath) async {
    try {
      final File file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      print('删除图片失败: $e');
    }
  }

  /// 获取图片文件大小（字节）
  static Future<int> getImageSize(String imagePath) async {
    try {
      final File file = File(imagePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      print('获取图片大小失败: $e');
      return 0;
    }
  }
}

/// 自定义裁剪比例预设
class CropAspectRatioPresetCustom implements CropAspectRatioPresetData {
  @override
  (int, int)? get data => (3, 4); // 3:4 比例

  @override
  String get name => '3:4';
}
