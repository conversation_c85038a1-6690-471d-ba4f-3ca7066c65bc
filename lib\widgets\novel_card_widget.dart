import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/services/image_crop_service.dart';
import 'package:novel_app/controllers/novel_controller.dart';

class NovelCardWidget extends StatelessWidget {
  final Novel novel;
  final VoidCallback? onTap;
  final bool showEditButton;

  const NovelCardWidget({
    super.key,
    required this.novel,
    this.onTap,
    this.showEditButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 140,
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // 左侧封面区域 (3:4 比例)
              _buildCoverSection(context),
              const SizedBox(width: 12),
              // 右侧信息区域
              Expanded(
                child: _buildInfoSection(context),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoverSection(BuildContext context) {
    return GestureDetector(
      onTap: showEditButton ? () => _showCoverOptions(context) : null,
      child: Container(
        width: 84, // 3:4 比例，高度为 112
        height: 112,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[200],
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: novel.coverImagePath != null && novel.coverImagePath!.isNotEmpty
              ? _buildCoverImage()
              : _buildPlaceholderCover(context),
        ),
      ),
    );
  }

  Widget _buildCoverImage() {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.file(
          File(novel.coverImagePath!),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildPlaceholderCover(context);
          },
        ),
        if (showEditButton)
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.edit,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceholderCover(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.3),
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            showEditButton ? Icons.add_photo_alternate : Icons.book,
            color: Theme.of(context).primaryColor,
            size: showEditButton ? 24 : 32,
          ),
          if (showEditButton) ...[
            const SizedBox(height: 4),
            Text(
              '添加封面',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 书名
        Text(
          novel.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        // 类型
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            novel.genre,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontSize: 12,
            ),
          ),
        ),
        const Spacer(),
        // 统计信息
        Row(
          children: [
            _buildStatItem(
              icon: Icons.text_fields,
              label: '${_formatWordCount(novel.wordCount)}字',
            ),
            const SizedBox(width: 12),
            _buildStatItem(
              icon: Icons.menu_book,
              label: '${novel.chapters.length}章',
            ),
          ],
        ),
        const SizedBox(height: 4),
        // 状态
        Row(
          children: [
            Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: _getStatusColor(),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              _getStatusText(),
              style: TextStyle(
                color: _getStatusColor(),
                fontSize: 12,
              ),
            ),
            const Spacer(),
            if (showEditButton)
              IconButton(
                icon: const Icon(Icons.more_vert),
                iconSize: 16,
                onPressed: () => _showMoreOptions(context),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem({required IconData icon, required String label}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _formatWordCount(int wordCount) {
    if (wordCount >= 10000) {
      return '${(wordCount / 10000).toStringAsFixed(1)}万';
    }
    return wordCount.toString();
  }

  Color _getStatusColor() {
    if (novel.content.isEmpty) {
      return Colors.grey;
    } else if (novel.chapters.isEmpty) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _getStatusText() {
    if (novel.content.isEmpty) {
      return '未开始';
    } else if (novel.chapters.isEmpty) {
      return '创作中';
    } else {
      return '已完成';
    }
  }

  void _showCoverOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('选择封面'),
              onTap: () {
                Navigator.pop(context);
                _selectCover();
              },
            ),
            if (novel.coverImagePath != null && novel.coverImagePath!.isNotEmpty)
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('删除封面'),
                onTap: () {
                  Navigator.pop(context);
                  _removeCover();
                },
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectCover() async {
    final String? imagePath = await ImageCropService.pickAndCropImage(
      aspectRatioX: 3,
      aspectRatioY: 4,
      title: '裁剪小说封面',
    );

    if (imagePath != null) {
      final novelController = Get.find<NovelController>();
      final updatedNovel = novel.copyWith(coverImagePath: imagePath);
      await novelController.updateNovel(updatedNovel);
      Get.snackbar('成功', '封面已更新');
    }
  }

  Future<void> _removeCover() async {
    if (novel.coverImagePath != null) {
      await ImageCropService.deleteImage(novel.coverImagePath!);
      final novelController = Get.find<NovelController>();
      final updatedNovel = novel.copyWith(coverImagePath: '');
      await novelController.updateNovel(updatedNovel);
      Get.snackbar('成功', '封面已删除');
    }
  }

  void _showMoreOptions(BuildContext context) {
    // 这里可以添加更多选项，如编辑、删除等
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('编辑小说'),
              onTap: () {
                Navigator.pop(context);
                // 跳转到编辑页面
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('删除小说'),
              onTap: () {
                Navigator.pop(context);
                _confirmDelete(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDelete(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除小说《${novel.title}》吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteNovel();
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteNovel() async {
    final novelController = Get.find<NovelController>();
    await novelController.deleteNovel(novel.id);
    Get.snackbar('成功', '小说已删除');
  }
}
